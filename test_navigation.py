#!/usr/bin/env python3
"""
Тестовый скрипт для проверки навигации в аналитике менеджера
"""

import asyncio
import sys
import os

# Добавляем корневую директорию в путь
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.database import init_db
from database.repositories import ManagerRepository, CuratorRepository, GroupRepository

async def test_manager_navigation():
    """Тестируем навигацию менеджера"""
    
    # Инициализируем базу данных
    await init_db()
    
    print("🔍 Тестируем навигацию менеджера в аналитике...")
    
    # Получаем менеджера
    managers = await ManagerRepository.get_all()
    if not managers:
        print("❌ Менеджеры не найдены")
        return
    
    manager = managers[0]
    print(f"✅ Найден менеджер: {manager.user.name}")
    
    # Получаем кураторов
    curators = await CuratorRepository.get_all()
    if not curators:
        print("❌ Кураторы не найдены")
        return
    
    curator = curators[0]
    print(f"✅ Найден куратор: {curator.user.name}")
    
    # Получаем группы куратора
    groups = await CuratorRepository.get_curator_groups(curator.id)
    print(f"✅ Группы куратора {curator.user.name}: {len(groups)} групп")
    
    for group in groups:
        print(f"   - {group.name} ({group.subject.name if group.subject else 'Без предмета'})")
    
    # Получаем все группы
    all_groups = await GroupRepository.get_all()
    print(f"✅ Всего групп в системе: {len(all_groups)}")
    
    print("\n🔍 Проверяем логику фильтрации:")
    print(f"   - Куратор {curator.user.name} имеет {len(groups)} групп")
    print(f"   - При выборе этого куратора должны показываться только его группы")
    print(f"   - При нажатии 'Назад' должны снова показываться только его группы")

if __name__ == "__main__":
    asyncio.run(test_manager_navigation())
